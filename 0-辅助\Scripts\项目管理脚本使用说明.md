# 项目管理脚本使用说明

## 脚本概述

`projectManager.js` 是一个基于 Templater 插件的项目管理脚本，提供了完整的项目创建和文件管理功能。

## 功能特性

### 1. ➕创建新项目
- 弹出输入弹窗让用户输入项目名称
- 在 `2-项目` 路径下创建新的项目文件夹
- 根据模板 "TP-Project-首页" 创建项目首页文件
- 自动在新标签页打开创建的首页文件

### 2. 【1】每周计划
- 根据模板 "TP-Project-Plan" 创建每周计划文件
- 文件保存在项目的 `1-每周计划` 子文件夹中
- 文件名格式：`Plan-YYYY-WKWW.md`（如：Plan-2025-WK30.md）

### 3. 【2】每日执行
- 根据模板 "TP-Project-Do" 创建每日执行文件
- 文件保存在项目的 `2-每日执行` 子文件夹中
- 文件名格式：`Do-YYYY-MM-DD.md`（如：Do-2025-07-24.md）

### 4. 【3】每周评审
- 根据模板 "TP-Project-Replay" 创建每周评审文件
- 文件保存在项目的 `3-每周评审` 子文件夹中
- 文件名格式：`Replay-YYYY-WKWW.md`（如：Replay-2025-WK30.md）

### 5. 【4】每周回顾
- 根据模板 "TP-Project-Review" 创建每周回顾文件
- 文件保存在项目的 `4-每周回顾` 子文件夹中
- 文件名格式：`Review-YYYY-WKWW.md`（如：Review-2025-WK30.md）

## 使用方法

### 配置脚本

1. 将 `projectManager.js` 文件放置在 `0-辅助/Scripts/` 目录下
2. 在 Templater 插件设置中添加脚本路径：`0-辅助/Scripts/projectManager.js`
3. 为脚本设置快捷键或命令（可选）

### 执行脚本

#### 方法一：通过 Templater 命令
1. 按 `Ctrl+P`（或 `Cmd+P`）打开命令面板
2. 输入 "Templater: Run Script" 并选择
3. 选择 `projectManager.js` 脚本

#### 方法二：通过快捷键（如果已配置）
直接按设置的快捷键执行脚本

#### 方法三：通过 Templater 模板调用
在任何模板文件中使用：
```javascript

```

### 使用场景

#### 创建新项目
1. 执行脚本
2. 选择 "➕创建新项目"
3. 输入项目名称（如："个人知识管理系统"）
4. 脚本会自动创建项目文件夹和首页文件

#### 创建项目文件
1. 在项目文件夹中的任意文件内执行脚本
2. 选择需要创建的文件类型
3. 脚本会根据当前日期自动生成文件名并创建文件

## 注意事项

### 文件命名规则
- **每周文件**：使用 ISO 周数格式 `YYYY-WKWW`
- **每日文件**：使用日期格式 `YYYY-MM-DD`
- 周数计算遵循 ISO 8601 标准

### 执行环境要求
- 创建项目文件时，必须在 `2-项目` 文件夹下的文件中执行脚本
- 如果目标文件已存在，脚本会直接打开现有文件而不是创建新文件

### 模板依赖
脚本依赖以下模板文件，请确保它们存在于 `0-辅助/Templater/Notes/` 目录中：
- `TP-Project-首页.md`
- `TP-Project-Plan.md`
- `TP-Project-Do.md`
- `TP-Project-Replay.md`
- `TP-Project-Review.md`

### 文件夹结构
脚本会自动创建以下文件夹结构：
```
2-项目/
└── [项目名称]/
    ├── [项目名称]-首页.md
    ├── 1-每周计划/
    ├── 2-每日执行/
    ├── 3-每周评审/
    └── 4-每周回顾/
```

## 错误处理

脚本包含完善的错误处理机制：
- 输入验证（空值检查）
- 文件存在性检查
- 模板文件验证
- 详细的错误提示信息

## 扩展建议

如需扩展功能，可以考虑：
1. 添加更多文件类型支持
2. 自定义文件命名规则
3. 集成更多项目管理功能
4. 添加批量操作功能
