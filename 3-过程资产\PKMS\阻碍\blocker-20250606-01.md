---
aliases:
  - 阻碍处理流程缺失
created_Date: 2025-06-06
status: 进行中
count: 3
timeCost: 1
drain_val: 3
relation:
  - "[[blocker-20250630-01]]"
cssclasses:
  - c3
---
# 1. 基础信息

- 现象描述：阻碍的具体处理方法是什么？何时应该处理阻碍？处理到什么程度？
- 直接影响：当周目标无法按时完成
- 衍生影响：

# 2. 临时方案

| 生效时间             | 目标             | 临时方案描述                                                                                                                                                                               | 决策依据                 | 已知风险与局限 | 状态跟踪 | 债务等级 | 知识缺口 |
| ---------------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------- | ------- | ---- | ---- | ---- |
| 2025-06-06 14:00 | 优先保证当前工作可以顺利推进 | （1）记录当前阻碍，并给出尝试方案<br>（2）执行当前尝试方案（IF 尝试方案有效，则记录具体方法；IF 尝试方案无效，则尝试给出其他方案并记录，依次循环；IF 当天无法完成，则将尝试方案转换为[每周计划]中的任务，并提高优先级（尝试安排计划时间））<br>（3）次日按照任务优先级完成相应任务，并循环上述步骤<br>（4）待周维度回顾时，查看并标记阻碍状态 | 执行尝试方案可以保证当前工作能够顺利开展 |         | 已失效  |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
