# 1. 缓存区

- 定义CSS片段，隐藏列表的延伸线
- 遇到阻碍时在2分钟内能够想到解决方案，但是尝试后发现并不能解决问题，此时阻碍应该如何记录？
- 若发现的阻碍（不处理无法继续工作）在2分钟内可以想到解决方案，但是处理结果和花费的时长不确定，那么这个阻碍还需要记录吗？
- 在`0-辅助`文件创建版本记录文档
- 在「每周回顾」“改进项梳理”显示“行动项”列表便于进行整体评估排序
- 在excallidraw中绘图时，频繁点击鼠标来使用“编辑”功能给工作带了困扰

# 2. 留观区

- 灵感VS下一步思考VS下一步工作？
- 「每周评审」“KR进度”关键字段“关联KR”链接非动态链接
- 在记录障碍日志时，是否所有的障碍都需要记录
- 使用脚本创建阻碍时报错：Templater Error:Template parsing error,aborting. check console for more information
- 根据「每日执行」模板新建文件时，提示：Templater Error: No active editor, can't append templates
- ”改善代办“与”闪念“管理的方式十分相似，但是发现的BUG或者其他类型的任务应该怎么办
- 使用JS脚本创建阻碍时，文件名称对应的时间与笔记属性的创建时间不一致
- 在查看“任务安排”中的任务信息时，是否需要显示每条任务的源文件链接？
- 在执行每日任务期间，若当日任务为持续性任务或一日不能完成的任务，那么应该如何记录和安排
- 在obsidian中新建、重新打开文件或重启软件时，鼠标光标的默认位置会影响文件的查看（特别是开头为代码块），不利于查看和编辑文档
- 在excallidraw绘图时，默认的字号类型不能覆盖全部的使用场景，导致部分场景下的图形布局杂乱或畸变
- 在项目推进（迭代）期间，KR进度中已关联的“证据”被优化或删除，原始文件是否需要同时删除？
- 在进行知识版本管理时，通过技术手段使知识组件笔记属性[update_Date]在修改完文件后自动更新，可以直观了解最新的修改状态

# 3. 行动区

- [ ] 编写自动创建「输出」、自动建立「输出」双链的JS脚本 🔽
- [ ] 优化阻碍创建JS脚本（笔记属性自动创建关联阻碍链接） 
- [ ] 优化「dataview-table-style.css」功能（表格宽度自适应） 
- [ ] 为「dashboard」添加“折线图”（添加周维度新增阻碍情况） 

