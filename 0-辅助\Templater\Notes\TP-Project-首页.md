---
startDate: ""
endDate: ""
---
# 1. OKR设定

- O：
- KR1：
- KR2：

# 2. 项目进度
```dataviewjs
	// 定义 getWeekNumber 函数
	function getWeekNumber(date) {
	    const d = new Date(date);
	    d.setHours(0, 0, 0, 0);
	    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
	    const week1 = new Date(d.getFullYear(), 0, 4);
	    return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
	}
	
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	// 获取当前日期并计算年份和上周周数
	const now = new Date();
	let year = now.getFullYear();
	let weekNumber = getWeekNumber(now) - 1;
	
	// 初始化查找结果
	let krContent = null;
	let foundWeek;
	let foundYear;
	
	// 尝试查找最近的周报（最多回溯4周）
	for (let i = 0; i < 4; i++) {
	    // 处理跨年情况（如果当前是第一周，则上周是去年的最后一周）
	    if (weekNumber < 1) {
	        year--;
	        weekNumber = 52; // ISO周数最大为52或53周
	    }
	    
	    // 动态生成文件名
	    const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	    const path = `2-项目/${projectName}/3-每周评审/${dynamicFilename}`;
	    const file = app.vault.getAbstractFileByPath(path);
	    
	    if (file) {
	        // 读取并解析文件
	        const content = await dv.io.load(path);
	        const headingRegex = /(?:^|\n)#{1,6}\s*.*?KR进度.*?(?:\n|$)([\s\S]*?)(?=\n?#{1,6}\s|$)/i;
	        const match = content.match(headingRegex);
	        
	        if (match) {
	            krContent = match[1].trim();
	            foundWeek = weekNumber;
	            foundYear = year;
	            break; // 找到有效内容，跳出循环
	        }
	    }
	    
	    // 准备回溯到更早的一周
	    weekNumber--;
	}
	
	// 输出结果
	if (krContent) {
	    dv.paragraph(krContent);
	} else {
	    dv.el("p", "未找到近期项目周报");
	}
```
# 3. 交付异常

```dataviewjs
	// 汇总表格数据到当前页面
	const projectName = dv.current().file.path.split("/")[1].trim();
	const targetFolder = `2-项目/${projectName}/3-每周评审`;
	const tableTitlePattern = "交付异常"; 
	
	// 使用当前日期计算周数
	const now = moment();
	const targetYear = now.isoWeekYear();
	const targetWeek = now.isoWeek();
		
	let allTableData = [];
	let filesProcessed = 0;
	let tablesFound = 0;
	let skippedFiles = 0;
	
	// 处理小于目标周数的所有Review文件
	for (let file of dv.pages(`"${targetFolder}"`).file) {
		const fileMatch = file.name.match(/^Replay-(\d{4})-WK(\d{2})$/);
		if (!fileMatch) {
			skippedFiles++;
			continue;
		}
		const fileYear = parseInt(fileMatch[1]);
		const fileWeek = parseInt(fileMatch[2]);
	
		// 仅处理周数严格小于当前的文件
		if (fileYear < targetYear || 
		   (fileYear === targetYear && fileWeek < targetWeek)) {
			
			filesProcessed++;
			const content = await dv.io.load(file.path);
			
			// 识别指定标题下的内容区域
			const headingRegex = new RegExp(
				`(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, 
				"i"
			);
			
			const match = content.match(headingRegex);
			if (!match || !match[1]) continue;
			
			const sectionContent = match[1].trim();
			
			// 提取表格数据
			const tableRegex = /^\s*\|(.+)\|\n\s*\|?\s*:?-+:?\s*\|.+\n((?:\s*\|.+\|\n?)+)/gms;
			let tableMatch;
			
			while ((tableMatch = tableRegex.exec(sectionContent)) !== null) {
				const headerRow = tableMatch[1].split('|')
					.map(cell => cell.trim()).filter(Boolean);
				let dataRows = tableMatch[2].split('\n')
					.filter(row => row.includes('|') && !row.startsWith('|-'))
					.map(row => 
						row.split('|')
						.slice(1, -1)
						.map(cell => cell.trim())
					);
				
				// 过滤掉内容为空的表格行
				dataRows = dataRows.filter(row => row.some(cell => cell !== ""));
				
				if (headerRow.length > 1 && dataRows.length > 0) {
					// 创建文件链接
					const weekTag = `${fileYear}-WK${fileWeek.toString().padStart(2,'0')}`;
					const fileName = `Replay-${weekTag}.md`;
					const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, weekTag);
					
					// 添加表头（首次识别时）
					if (allTableData.length === 0) {
						allTableData.push(["周", ...headerRow]);
					}
					
					// 添加数据行（使用文件链接对象）
					dataRows.forEach(row => {
						allTableData.push({
							// 存储排序用的数值
							year: fileYear,
							week: fileWeek,
							// 存储实际显示的行数据
							rowData: [fileLink, ...row]
						});
					});
					
					tablesFound++;
				}
			}
		} else {
			skippedFiles++;
		}
	}
	
	// 新增排序功能 - 按周数倒序（最近的在前）
	if (allTableData.length > 0) {
		// 分离表头（第一行）
		const headerRow = allTableData[0];
		
		// 排序数据行（按年份倒序，再按周倒序）
		const sortedData = allTableData.slice(1)
			.sort((a, b) => {
				// 先比较年份
				if (b.year !== a.year) {
					return b.year - a.year; // 年份越大越靠前
				}
				// 同年份比较周数
				return b.week - a.week; // 周数越大越靠前
			})
			.map(item => item.rowData); // 只提取行数据
		
		// 渲染表格（表头 + 排序后的数据）
		dv.table(headerRow, sortedData);
	} else {
		dv.el("p", "🎉 无历史数据");
	}
```

# 4. 行动变更

| 变更日期 | 计划/行动名称 | 决策类型 | 决策依据 | 决策失误原因 | 关键证据 | 新行动 |
| ---- | ------- | ---- | ---- | ------ | ---- | --- |
|      |         |      |      |        |      |     |

