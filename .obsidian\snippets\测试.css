/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
/* 纯粹使用 list-style-type 修改默认符号类型 */

/* 第一层：实心圆点 */
.markdown-preview-view ul > li {
    list-style-type: disc;
}
.markdown-preview-view ul > li::marker {
    color: #666;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li {
    list-style-type: circle;
}
.markdown-preview-view ul ul > li::marker {
    color: #888;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li {
    list-style-type: square;
}
.markdown-preview-view ul ul ul > li::marker {
    color: #999;
}

/* 第四层：空心方块（使用 disclosure-open） */
.markdown-preview-view ul ul ul ul > li {
    list-style-type: disclosure-open;
}
.markdown-preview-view ul ul ul ul > li::marker {
    color: #aaa;
}

/* 第五层：菱形（使用 disclosure-closed） */
.markdown-preview-view ul ul ul ul ul > li {
    list-style-type: disclosure-closed;
}
.markdown-preview-view ul ul ul ul ul > li::marker {
    color: #bbb;
}

/* 第六层及以上：使用默认disc */
.markdown-preview-view ul ul ul ul ul ul > li {
    list-style-type: disc;
}
.markdown-preview-view ul ul ul ul ul ul > li::marker {
    color: #ccc;
}

/* 编辑模式下的无序列表符号设计 */
/* 直接修改符号颜色，不创建新元素 */

/* 第一层：实心圆点 */
.cm-editor .HyperMD-list-line-1 .cm-formatting-list-ul {
    color: #666 !important;
}

/* 第二层：空心圆点 */
.cm-editor .HyperMD-list-line-2 .cm-formatting-list-ul {
    color: #888 !important;
}

/* 第三层：实心方块 */
.cm-editor .HyperMD-list-line-3 .cm-formatting-list-ul {
    color: #999 !important;
}

/* 第四层：空心方块 */
.cm-editor .HyperMD-list-line-4 .cm-formatting-list-ul {
    color: #aaa !important;
}

/* 第五层：菱形 */
.cm-editor .HyperMD-list-line-5 .cm-formatting-list-ul {
    color: #bbb !important;
}

/* 第六层及以上：三角形 */
.cm-editor .HyperMD-list-line-6 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-7 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-8 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-9 .cm-formatting-list-ul {
    color: #ccc !important;
}