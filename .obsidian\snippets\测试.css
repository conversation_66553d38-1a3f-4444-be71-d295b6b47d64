/* 隐藏列表连接线但保留项目符号 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏编辑模式下列表项之间的垂直连接线 */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏编辑模式下嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线（更精确的选择器） */
.markdown-preview-view ul::before,
.markdown-preview-view ol::before,
.markdown-preview-view li::before {
    display: none !important;
}

/* 隐藏预览模式下列表项的连接线装饰 */
.markdown-preview-view .list-bullet::before,
.markdown-preview-view .list-number::before {
    display: none !important;
}

/* 确保项目符号正常显示并垂直居中 */
.markdown-preview-view ul,
.markdown-preview-view ol {
    list-style-position: outside !important;
    padding-left: 1.5em !important;
}

/* 无序列表项目符号设置 */
.markdown-preview-view ul li {
    list-style-type: disc !important;
    display: list-item !important;
    position: relative;
    line-height: 1.6 !important;
    margin: 0.2em 0 !important;
}

/* 有序列表项目符号设置 */
.markdown-preview-view ol li {
    list-style-type: decimal !important;
    display: list-item !important;
    position: relative;
    line-height: 1.6 !important;
    margin: 0.2em 0 !important;
}

/* 使用 ::marker 精确控制项目符号位置 */
.markdown-preview-view ul li::marker {
    content: "•" !important;
    color: var(--text-normal) !important;
    font-size: 1em !important;
    line-height: 1.6 !important;
}

.markdown-preview-view ol li::marker {
    color: var(--text-normal) !important;
    font-size: 1em !important;
    line-height: 1.6 !important;
}

/* 确保列表项内容正确对齐 */
.markdown-preview-view ul li > *,
.markdown-preview-view ol li > * {
    vertical-align: baseline !important;
    line-height: 1.6 !important;
}