/* 隐藏列表连接线但保留项目符号 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏编辑模式下列表项之间的垂直连接线 */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏编辑模式下嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线（更精确的选择器） */
.markdown-preview-view ul::before,
.markdown-preview-view ol::before,
.markdown-preview-view li::before {
    display: none !important;
}

/* 隐藏预览模式下列表项的连接线装饰 */
.markdown-preview-view .list-bullet::before,
.markdown-preview-view .list-number::before {
    display: none !important;
}

/* 确保项目符号正常显示 */
.markdown-preview-view .list-bullet,
.markdown-preview-view .list-number {
    display: list-item !important;
}

/* 确保有序列表和无序列表的标记正常显示 */
.markdown-preview-view ul li {
    list-style-type: disc !important;
}

.markdown-preview-view ol li {
    list-style-type: decimal !important;
}